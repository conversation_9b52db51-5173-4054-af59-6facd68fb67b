'use client';
import React from 'react';
import {View, Text} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {components} from '../components';
import useAddressCreation from '../modules/checkout/hooks/addresses/use-address-creation';
import useCountries from '../modules/checkout/hooks/addresses/use-countries';
import useCountryCities from '../modules/checkout/hooks/addresses/use-country-cities';
import AddressDropdownMenu from '../modules/checkout/components/addresses/address-dropdown-menu';

const AddANewAddress: React.FC = (): JSX.Element => {
  const {
    createAddress,
    formData,
    handleInputChange,
    isLoading,
    warning,
    wrongInputs,
  } = useAddressCreation();
  const {country, countryOptionnalLabels} = useCountries();
  const {cities, city, changeCity, citiesAreLoading} = useCountryCities(
    country?.code || '',
  );

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Add a new address' goBack={true} />;
  };

  const renderContent = () => {
    if (
      !country ||
      (country.code === 'TN' &&
        (!cities || cities.length === 0 || citiesAreLoading))
    ) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}
        >
          <Text style={{color: '#8B9DC3', fontSize: 16}}>
            {citiesAreLoading
              ? 'Loading cities...'
              : 'Loading address fields...'}
          </Text>
        </View>
      );
    }

    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 40,
          paddingBottom: 20,
        }}
        enableOnAndroid={true}
        showsVerticalScrollIndicator={false}
        style={{flexGrow: 0}}
      >
        {warning && (
          <View style={{marginBottom: 20}}>
            <Text style={{color: '#E82837', textAlign: 'center', fontSize: 14}}>
              {warning}
            </Text>
          </View>
        )}
        <View style={{marginBottom: 22}}>
          <components.InputField
            label='Email'
            placeholder='Enter your email'
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            keyboardType='email-address'
            containerStyle={{
              borderColor: wrongInputs.includes('email')
                ? '#E82837'
                : '#DBE9F5',
            }}
          />
        </View>
        {country.formatting.show.split('_').map((inputs, idx) => {
          const inputsLabels = inputs.split(' ');
          console.log(inputsLabels);
          return (
            <View key={idx} style={{flexDirection: 'row', marginBottom: 22}}>
              {inputsLabels.map((input) => {
                const fieldName = input.substring(1, input.length - 1);
                if (fieldName === 'country') return null;
                const isOptional = countryOptionnalLabels.includes(fieldName);
                const label =
                  fieldName === 'country'
                    ? 'Country'
                    : fieldName === 'phone'
                    ? 'Phone Number'
                    : fieldName.charAt(0).toUpperCase() +
                      fieldName
                        .slice(1)
                        .replace(/([A-Z])/g, ' $1')
                        .trim();
                const placeholder = isOptional
                  ? `${label} (Optional)`
                  : `Enter ${label.toLowerCase()}`;

                return (
                  <View
                    key={fieldName}
                    style={{
                      flex: inputsLabels.length > 1 ? 1 : undefined,
                      marginRight: inputsLabels.length > 1 ? 10 : 0,
                      width: inputsLabels.length > 1 ? undefined : '100%',
                    }}
                  >
                    {country.code === 'TN' &&
                    fieldName === 'city' &&
                    cities.length > 0 ? (
                      <View style={{marginBottom: 22}}>
                        <Text
                          style={{
                            fontSize: 12,
                            fontWeight: '500',
                            textTransform: 'uppercase',
                            color: '#8B9DC3',
                            marginBottom: 8,
                            marginLeft: 4,
                          }}
                        >
                          City
                        </Text>
                        <AddressDropdownMenu
                          data={cities}
                          selectedElement={city}
                          onChange={(cityCode: string) => {
                            changeCity(cityCode);
                            handleInputChange('city', city.name);
                          }}
                          primaryTheme={false}
                        />
                      </View>
                    ) : (
                      <components.InputField
                        label={label}
                        placeholder={
                          fieldName === 'phone'
                            ? `+${country.phoneNumberPrefix} ${placeholder}`
                            : placeholder
                        }
                        // value={formData[fieldName]}
                        onChangeText={(value) =>
                          handleInputChange(fieldName, value)
                        }
                        keyboardType={
                          fieldName === 'email'
                            ? 'email-address'
                            : fieldName === 'phone'
                            ? 'phone-pad'
                            : 'default'
                        }
                        containerStyle={{
                          marginBottom: 22,
                          borderColor: wrongInputs.includes(fieldName)
                            ? '#E82837'
                            : '#DBE9F5',
                        }}
                      />
                    )}
                  </View>
                );
              })}
            </View>
          );
        })}
      </KeyboardAwareScrollView>
    );
  };

  const renderButton = () => {
    return (
      <components.Button
        title={isLoading ? 'Saving...' : 'Save Address'}
        onPress={createAddress}
        containerStyle={{
          margin: 20,
        }}
      />
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderButton()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default AddANewAddress;
